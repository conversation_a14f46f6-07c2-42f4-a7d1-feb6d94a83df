# TradingPipelineRegistry 模組文檔

## 📋 概述

TradingPipelineRegistry 是 PipelineAdvance_v1 模組中的一個新組件，專門負責管理和註冊交易階段（ENUM_TRADING_STAGE）和交易事件（ENUM_TRADING_EVENT）到容器管理器中。

## 🎯 設計目標

- **統一註冊管理**: 提供統一的介面來註冊交易階段和事件
- **自動容器創建**: 當註冊階段或事件時，自動創建對應的容器
- **錯誤處理**: 完善的錯誤檢查和處理機制
- **狀態追蹤**: 追蹤已註冊的階段和事件狀態

## 🏗️ 類別結構

### 核心成員變數

```cpp
class TradingPipelineRegistry
{
private:
    TradingPipelineContainerManager* m_manager;         // 容器管理器指針
    HashMap<int, ITradingPipeline*> m_registeredStages; // 已註冊的交易階段
    HashMap<int, ITradingPipeline*> m_registeredEvents; // 已註冊的交易事件
    string m_name;                                       // 註冊器名稱
    string m_type;                                       // 註冊器類型
    int m_maxRegistrations;                              // 最大註冊數量
    bool m_isEnabled;                                    // 是否啟用
};
```

### 核心方法

#### 構造函數

```cpp
TradingPipelineRegistry(TradingPipelineContainerManager* manager,
                       string name = "TradingPipelineRegistry",
                       string type = "PipelineRegistry",
                       int maxRegistrations = 50)
```

#### 註冊方法

```cpp
bool Register(ENUM_TRADING_STAGE stage, ITradingPipeline* pipeline)    // 註冊交易階段
bool Register(ENUM_TRADING_EVENT event, ITradingPipeline* pipeline)    // 註冊交易事件
```

#### 查詢方法

```cpp
bool IsStageRegistered(ENUM_TRADING_STAGE stage) const
bool IsEventRegistered(ENUM_TRADING_EVENT event) const
ITradingPipeline* GetRegisteredStagePipeline(ENUM_TRADING_STAGE stage) const
ITradingPipeline* GetRegisteredEventPipeline(ENUM_TRADING_EVENT event) const
int GetRegisteredStageCount() const
int GetRegisteredEventCount() const
int GetTotalRegistrations() const
```

#### 管理方法

```cpp
bool UnregisterStage(ENUM_TRADING_STAGE stage)
bool UnregisterEvent(ENUM_TRADING_EVENT event)
void Clear()
void SetEnabled(bool enabled)
```

## 📖 使用方法

### 基本使用

```cpp
// 創建容器管理器
TradingPipelineContainerManager* manager =
    new TradingPipelineContainerManager("我的管理器");

// 創建註冊器
TradingPipelineRegistry* registry =
    new TradingPipelineRegistry(manager, "我的註冊器");

// 創建流水線
TradingPipelineContainer* initPipeline = new TradingPipelineContainer("初始化流水線", "初始化", INIT_START);
TradingPipelineContainer* tickPipeline = new TradingPipelineContainer("交易流水線", "交易", TICK_DATA_FEED);
TradingPipelineContainer* deinitPipeline = new TradingPipelineContainer("清理流水線", "清理", DEINIT_CLEANUP);

// 註冊交易事件
registry.Register(TRADING_INIT, initPipeline);
registry.Register(TRADING_TICK, tickPipeline);
registry.Register(TRADING_DEINIT, deinitPipeline);

// 創建階段流水線
TradingPipelineContainer* startPipeline = new TradingPipelineContainer("開始階段", "開始", INIT_START);
TradingPipelineContainer* feedPipeline = new TradingPipelineContainer("數據饋送", "數據", TICK_DATA_FEED);
TradingPipelineContainer* cleanupPipeline = new TradingPipelineContainer("清理階段", "清理", DEINIT_CLEANUP);

// 註冊交易階段
registry.Register(INIT_START, startPipeline);
registry.Register(TICK_DATA_FEED, feedPipeline);
registry.Register(DEINIT_CLEANUP, cleanupPipeline);

// 檢查註冊狀態
if(registry.IsStageRegistered(INIT_START))
{
    Print("INIT_START 已註冊");
}

// 清理資源
delete registry;
delete manager;
```

### 進階使用

```cpp
// 創建有限制的註冊器
TradingPipelineRegistry* registry =
    new TradingPipelineRegistry(manager, "限制註冊器", "LimitedRegistry", 10);

// 批量註冊階段
ENUM_TRADING_STAGE stages[] = {INIT_START, INIT_PARAMETERS, INIT_VARIABLES};
for(int i = 0; i < ArraySize(stages); i++)
{
    // 為每個階段創建流水線
    string stageName = TradingEventUtils::StageToString(stages[i]);
    TradingPipelineContainer* stagePipeline = new TradingPipelineContainer(
        stageName + "流水線",
        stageName + "階段",
        stages[i]
    );

    if(!registry.Register(stages[i], stagePipeline))
    {
        Print("註冊階段失敗: ", stageName);
    }
}

// 獲取統計信息
Print("總註冊數: ", registry.GetTotalRegistrations());
Print("最大註冊數: ", registry.GetMaxRegistrations());
Print("是否已滿: ", registry.IsFull() ? "是" : "否");
```

## 🔧 功能特性

### 1. 自動容器創建

當註冊階段或事件時，如果對應的容器不存在，註冊器會自動創建：

```cpp
// 註冊階段時自動創建容器
TradingPipelineContainer* stagePipeline = new TradingPipelineContainer("階段流水線", "階段", INIT_START);
registry.Register(INIT_START, stagePipeline);  // 自動創建 TRADING_INIT 事件的容器

// 註冊事件時自動創建容器
TradingPipelineContainer* eventPipeline = new TradingPipelineContainer("事件流水線", "事件", TICK_DATA_FEED);
registry.Register(TRADING_TICK, eventPipeline); // 自動創建 TRADING_TICK 事件的容器
```

### 2. 階段到事件的映射

註冊器自動將階段映射到對應的事件：

- `INIT_START` ~ `INIT_COMPLETE` → `TRADING_INIT`
- `TICK_DATA_FEED` ~ `TICK_LOGGING` → `TRADING_TICK`
- `DEINIT_CLEANUP` ~ `DEINIT_COMPLETE` → `TRADING_DEINIT`

### 3. 完善的錯誤處理

- 檢查管理器指針是否為 NULL
- 檢查流水線指針是否為 NULL
- 檢查階段和事件的有效性
- 檢查重複註冊
- 檢查最大註冊數量限制
- 檢查註冊器啟用狀態

### 4. 狀態管理

- 追蹤已註冊的階段和事件
- 提供統計信息
- 支援啟用/禁用功能
- 支援清理和重置

## 📝 注意事項

1. **流水線生命週期**: 註冊器不擁有流水線對象，使用者需要負責流水線的生命週期管理
2. **線程安全**: 此類不是線程安全的，需要在單線程環境中使用
3. **記憶體管理**: 使用者需要負責正確刪除註冊器、管理器和流水線實例
4. **最大限制**: 預設最大註冊數量為 50，可在構造時自定義
5. **流水線指針**: 註冊時必須提供有效的 ITradingPipeline\* 指針，不能為 NULL

## 🧪 測試

運行測試腳本來驗證功能：

```cpp
#include "examples/TradingPipelineRegistryExample.mqh"

void OnStart()
{
    TradingPipelineRegistryExample::RunAllExamples();
}
```

## 🔗 相關類別

- `TradingPipelineContainerManager`: 容器管理器
- `TradingPipelineContainer`: 交易流水線容器
- `TradingEventUtils`: 交易事件工具類
- `HashMap`: 哈希映射容器

## 📈 版本歷史

- **v1.0**: 初始版本，實現基本的註冊和管理功能
